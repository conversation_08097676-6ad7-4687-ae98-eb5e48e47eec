# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
import re
from google import genai
from google.genai import types
import json
from typing import Dict, Any


# def clean_json_response(response_text: str) -> str:
#     """
#     Clean AI response text to make it valid JSON by removing control characters
#     and fixing common formatting issues.

#     Args:
#         response_text: Raw response text from AI model

#     Returns:
#         Cleaned text ready for JSON parsing
#     """
#     # Remove any markdown code block formatting
#     cleaned = response_text.strip()
#     if cleaned.startswith('```json'):
#         cleaned = cleaned[7:]
#     if cleaned.startswith('```'):
#         cleaned = cleaned[3:]
#     if cleaned.endswith('```'):
#         cleaned = cleaned[:-3]

#     # Remove control characters except for allowed ones (newlines in strings are OK)
#     # This regex removes control characters but preserves newlines within quoted strings
#     cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)

#     # Fix common JSON issues
#     cleaned = cleaned.strip()

#     return cleaned


# def safe_json_parse(response_text: str) -> Dict[str, Any]:
#     """
#     Safely parse JSON response with error handling and fallback.

#     Args:
#         response_text: Raw response text from AI model

#     Returns:
#         Parsed JSON as dictionary, or error structure if parsing fails
#     """
#     try:
#         # First, clean the response
#         cleaned_text = clean_json_response(response_text)

#         # Try to parse the cleaned JSON
#         return json.loads(cleaned_text)

#     except json.JSONDecodeError as e:
#         print(f"JSON parsing error: {e}")
#         print(f"Problematic text (first 500 chars): {response_text[:500]}")

#         # Return a fallback structure
#         return {
#             "title": "Generated Story Tasks",
#             "tasks": [],
#             "error": f"JSON parsing failed: {str(e)}",
#             "raw_response": response_text[:1000]  # Include first 1000 chars for debugging
#         }
#     except Exception as e:
#         print(f"Unexpected error in JSON parsing: {e}")
#         return {
#             "title": "Generated Story Tasks",
#             "tasks": [],
#             "error": f"Unexpected error: {str(e)}"
#         }



async def generate(audio_data: bytes, age: int = 10, num_tasks: int = 5, theme_id: str = None) -> Dict[str, Any]:
    """
    Generate structured tasks from audio data using Gemini with structured output.

    Args:
        audio_data: Audio bytes to process
        age: Target age for the content (default: 10)
        num_tasks: Number of tasks to generate (default: 5)
        theme_id: Optional theme ID to associate with the generated content

    Returns:
        Dictionary containing generated tasks and metadata
    """
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="audio/mp4",
                    data=audio_data
                ),
            ],
        )
    ]
    # Format the system instruction with actual values
    generate_content_config = types.GenerateContentConfig(
        temperature=0,
        response_mime_type="application/json",
        system_instruction=[
            types.Part.from_text(text="""
You are a Nepali tutor creating a fun, interactive **story-based quiz** for **{age}-year-old children**, using only the input **Nepali audio**.

---

### 🎯 Goal

* Generate a continuous story in **{num_tasks} chapters**
* Use **only the given audio**
* Make it **vivid, age-appropriate**, and include **one real-world object or sound** per chapter (animal, place, item, or natural sound)

---

### 🧩 Chapter Output Format

Each chapter includes:

#### 1. `\"story\"` object:

```json
\"story\": {{
  \"stage\": "Title of the chapter in Nepali\",
    "Thumbnail":"THumbnail Icon for the story",
  \"script\": \"Nepali paragraph (6–7 lines, storybook style)\",
  \"image\": \"Short English visual description (e.g., Child near river, Elephant trumpeting)\",
  \"audio\": \"One Nepali word or sound reference from the story (e.g., हात्ती, घण्टी)\"
}}
```

#### 2. `\"question\"` object:

Cycle through the following types per chapter:

> `single_choice` → `multiple_choice` → `image_identification` → `word_identification` → `speak_word` → repeat

```json
\"question\": {{
  \"type\": \"single_choice\" | \"multiple_choice\" | \"image_identification\" | \"word_identification\" | \"speak_word\",
  \"text\": \"Question in Nepali\",
  \"translated_text\": \"English translation\",
  \"options\": {{
    \"a\": \"घोड़ा\",
    \"b\": \"कुकुर\",
    \"c\": \"घण्टी\"
  }},
  \"answer_hint\": \"bell sound\",   // or घण्टी for speak_word
  \"answer\": \"c\"
}}
```

---

### ✅ Story Writing Rules

* Use storybook tone: “एक बिहान…”, “अचानक…”, “त्यसपछि…”
* Each chapter is a **logical continuation** of the last
* Include **sensory details** (what was heard, seen, felt)
* Emphasize **real-world items or sounds** (dog, river, bell, rain, horn)

---

### 🧠 Question Guidelines

| Type                    | Description                                                                              |
| ----------------------- | ---------------------------------------------------------------------------------------- |
| `single_choice`         | One correct answer from story                                                            |
| `multiple_choice`       | Two correct answers                                                                      |
| `image_identification`  | Ask “What do you see?” — don’t name the object directly                                  |
| `word_identification` ✅ | Ask “What did you hear?” — the **sound itself**, not the word alone (e.g., घण्टीको आवाज) |
| `speak_word`            | Prompt child to speak a Nepali word (same as `audio`)                                    |

#### Additional Rules:

* Options must be **single Nepali words**
* `\"speak_word\"` has no options — only word to speak
* `\"answer_hint\"`:

  * Use **English phrase** for `image_identification`  (e.g.,image of  bell , image of dog , Cow Eating Grass)
  * Use **Nepali word**  for `speak_word` and `word_identification (e.g., बाँदर, घण्टी)

---

### 📘 Updated Example (`word_identification` with sound focus)

```json
  "title":"Title of the story in nepali (घरका जनावरहरू)",  
  "title_en": "Title of the story in english",
  "thumbnail":"THumbnail Icon for the story",
  "description": "Description of the  task set in nepali (घरमा पाइने जनावरहरू चिन्नुहोस्)",
  "description_en": "Description of the story in english",
                                     
                                         
"output":[ 
  {{
    \"title\": \"सानी मायाको रमाइलो यात्रा\",
    \"tasks\": [
    {{
      \"title\": \"घण्टीको आवाज\",
      \"type\": \"word_identification\",
      \"story\": {{
        \"stage\": 1,
        \"script\": \"एक बिहान, सानी माया मन्दिरतिर गइन्। अचानक ठिङ्गठिङ्ग गर्दै ठूलो घण्टी बज्यो। माया अचम्मित भइन्। वरिपरि धेरै मानिसहरू थिए, सबैले हात जोडेर प्रार्थना गरिरहेका थिए। हावामा अगरबत्तीको सुगन्ध थियो।\",
        \"image\": \"Temple bell ringing\",
        \"audio\": \"घण्टी\"
      }},
      \"question\": {{
        \"type\": \"word_identification\",
        \"text\": \"कुन आवाज सुन्न सकिन्छ?\",
        \"translated_text\": \"Which sound can be heard?\",
        \"options\": {{
          \"a\": \"कुकुर\",
          \"b\": \"घण्टी\",
          \"c\": \"बाघ\"
        }},
        \"answer_hint\": \"bell ringing\",
        \"answer\": \"b\"
      }},
      \"max_score\": 10,
      \"complexity\": 1
    }}
    ]
  }}
]
```
""".format(age=age, num_tasks=num_tasks)       )
        ],
    )

 

    # for chunk in client.models.generate_content_stream(
    #     model=model,
    #     contents=contents,
    #     config=generate_content_config,
    # ):
    #     print(chunk.text, end="")

    response = client.models.generate_content(
        model=model,
        contents=contents,
        config=generate_content_config,
    )

    # task = safe_json_parse(response.text)
    usage = response.usage_metadata
    try:
      task = json.loads(response.text)
    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")
        print(f"Problematic text (first 500 chars): {response.text[:500]}")

        # Return a fallback structure
        task = {
            "title": "Generated Story Tasks",
            "tasks": [],
            "error": f"JSON parsing failed: {str(e)}",
            "raw_response": response.text[:1000]  # Include first 1000 chars for debugging
        }
    # Debug: print the response structure
    print(f"Response type: {type(task)}")
    print(f"Response content: {task}")


    # Add theme_id to the task data if provided
    if theme_id:
        task["theme_id"] = theme_id

    return task, usage.model_dump()

if __name__ == "__main__":
    with open("generated_audio.wav", "rb") as audio_file:
        audio_data = audio_file.read()
    import asyncio
    result = asyncio.run(generate(audio_data))
    print(f"Generated Task: {json.dumps(result, ensure_ascii=False, indent=2)}")
    print(f"Total tokens used: {result['metadata']['usage']}")

