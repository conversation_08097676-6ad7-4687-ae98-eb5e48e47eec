"""
Task models for quiz and task management.
"""
from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Any, Dict, List, Optional, Union, Literal
from datetime import datetime
from enum import Enum
from bson import ObjectId

from app.shared.object_id import PyObjectId, object_id_field, validate_object_id
from app.shared.db_enums import TaskStatus, TaskResult, InputType, QuizType, ScoreValue, VerificationStatus, DifficultyLevel, GenerationType

class MediaType(str, Enum):
    """Enum for media types."""
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    OTHER = "other"


class MinioObject(BaseModel):
    """Model for object details in Minio storage."""
    object_name: str
    folder: str
    media_type: MediaType = MediaType.OTHER
    bucket_name: Optional[str] = None
    content_type: Optional[str] = None
    size_bytes: Optional[int] = None
    original_filename: Optional[str] = None

    def get_path(self) -> str:
        """Get the full path to the object in the format folder/object_name."""
        if self.folder is None:
            return self.object_name
        return f"{self.folder}/{self.object_name}"


class Story(BaseModel):
    """Model for story content in story-based tasks."""
    stage: int = Field(..., description="Chapter/stage number (1-5)")
    script: str = Field(..., description="Story script in Nepali")
    image: Optional[str] = Field(None, description="Image description for the scene")
    media_url: Optional[str] = Field(None, description="URL for story media/image")
    metadata: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )


class Question(BaseModel):
    """Model for a question in a task."""
    type: QuizType
    text: Optional[str] = None
    translated_text: Optional[str] = None  # English translation/hint
    options: Optional[Union[List[str], Dict[str, str]]] = None  # Support both list and dict formats
    word: Optional[str] = None
    answer_hint: Optional[str] = None  # Hint for the correct answer
    media_url: Optional[str] = None  # Direct media URL (for compatibility)
    media: Optional[List[MinioObject]] = None  # List of media objects (images, audio, etc.)
    metadata: Optional[Dict[str, Any]] = None


class Answer(BaseModel):
    """Model for an answer to a question."""
    type: QuizType
    value: Any  # The actual answer value (text, option index, etc.)
    media: Optional[List[MinioObject]] = None  # List of media objects (images, audio, etc.)
    metadata: Optional[Dict[str, Any]] = None


class TaskItem(BaseModel):
    """Model for a single task item within a task set."""
    id: PyObjectId = object_id_field()
    type: QuizType

    title: Optional[str] = None

    # Core task content
    question: Question
    correct_answer: Answer
    user_answer: Optional[Answer] = None  # The user's answer, if provided

    # Story content (for story-based tasks)
    story: Optional[Story] = None  # Story content associated with this task

    # Status fields
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[TaskResult] = None  # None until answered
    remark: Optional[str] = None  # Additional comments or feedback

    # Timestamp fields
    created_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    submitted_at: Optional[datetime] = None
    verified_at: Optional[datetime] = None
    answered_at: Optional[datetime] = None

    # User reference fields
    submitted_by: Optional[str] = None
    verified_by: Optional[str] = None

    # Verification fields
    verification_status: VerificationStatus = VerificationStatus.PENDING
    verification_notes: Optional[str] = None

    # Testing fields
    test_status: Optional[str] = None
    test_results: Optional[Dict[str, Any]] = None

    # Scoring and submission tracking - Standardized naming
    total_score: int = Field(default=10)  # Maximum possible score for this task item
    scored: int = Field(default=0)  # Score actually earned for this task item
    submitted: bool = Field(default=False)  # Whether this task has been submitted at least once
    attempts_count: int = Field(default=0)  # Number of times this specific task has been attempted
    is_attempted: bool = Field(default=False)  # Whether this task has been attempted at least once

    # Difficulty level (1=easy, 2=medium, 3=hard)
    difficulty_level: Optional[int] = Field(None, description="Difficulty level of this task item (1=easy, 2=medium, 3=hard)")

    # Additional metadata
    metadata: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_encoders={ObjectId: str}
    )

    @field_validator("id", mode="before")
    @classmethod
    def validate_object_id(cls, v):
        return validate_object_id(v)


class TaskSet(BaseModel):
    """Model for a set of tasks generated from user input."""
    id: PyObjectId = object_id_field()
    user_id: str
    input_type: InputType
    input_content: Optional[str] = None
    input_data: Optional[bytes] = None
    tasks: List[str] = Field(default_factory=list)  # List of task IDs, not full TaskItem objects

    # Timestamp fields
    created_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    submitted_at: Optional[datetime] = None
    verified_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Status fields
    status: TaskStatus = TaskStatus.PENDING

    # Difficulty level from user profile (1=easy, 2=medium, 3=hard)
    difficulty_level: Optional[int] = Field(None, description="Difficulty level used for task generation (1=easy, 2=medium, 3=hard)")

    # Followup generation fields
    gentype: Optional[str] = Field(default="primary", description="Generation type: primary or follow_up")
    is_followup: bool = Field(default=False, description="Whether this is a followup task set")
    parent_task_set_id: Optional[PyObjectId] = Field(None, description="Parent task set ID if this is a followup")
    followup_count: int = Field(default=0, description="Current followup level/depth (0=primary, 1-3=followup levels)")
    original_task_set_id: Optional[PyObjectId] = Field(None, description="Original primary task set ID for all followup levels")

    # Followup level completion tracking (only for original/primary task sets)
    # Dynamic structure: {"1": true, "2": false, "3": true} etc.
    followup_levels_completed: Optional[Dict[str, bool]] = Field(default_factory=dict, description="Dynamic tracking of which followup levels have been completed")

    # Counter fields - Standardized naming
    total_tasks: int = Field(default=0)  # Total number of tasks in this set
    attempted_tasks: int = Field(default=0)  # Number of tasks that have been attempted at least once
    total_verified: int = Field(default=0)  # Number of tasks that have been verified

    # User reference fields
    created_by: Optional[str] = None
    verified_by: Optional[str] = None

    # Scoring fields - Standardized naming
    total_score: int = Field(default=0)  # Maximum possible score for all tasks in this set (sum of all task total_scores)
    scored: int = Field(default=0)  # Total score earned across all tasks in this set (sum of all task scored values)
    attempts_count: int = Field(default=0)  # Number of times this task set has been attempted

    remark: Optional[str] = None  # Additional comments about the task set

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_encoders={ObjectId: str}
    )

    @field_validator("id", mode="before")
    @classmethod
    def validate_object_id(cls, v):
        return validate_object_id(v)

    @field_validator("parent_task_set_id", mode="before")
    @classmethod
    def validate_parent_task_set_id(cls, v):
        if v is not None:
            return validate_object_id(v)
        return v

    @field_validator("original_task_set_id", mode="before")
    @classmethod
    def validate_original_task_set_id(cls, v):
        if v is not None:
            return validate_object_id(v)
        return v

    @field_validator("status", mode="before")
    @classmethod
    def update_status_if_all_submitted(cls, v, info):
        """Automatically update status to completed if all tasks are submitted."""
        if v != TaskStatus.COMPLETED and info.data.get("total_tasks") > 0:
            if info.data.get("attempted_tasks") == info.data.get("total_tasks"):
                return TaskStatus.COMPLETED
        return v


class TaskResponse(BaseModel):
    """Response model for task retrieval."""
    id: str
    tasks: List[Dict[str, Any]]
    status: TaskStatus
    input_type: InputType

    # Timestamp fields
    created_at: datetime
    submitted_at: Optional[datetime] = None
    verified_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Counter fields - Standardized naming
    total_tasks: int = Field(default=0)  # Total number of tasks in this set
    attempted_tasks: int = Field(default=0)  # Number of tasks that have been attempted at least once
    total_verified: int = Field(default=0)  # Number of tasks that have been verified

    # User reference fields
    created_by: Optional[str] = None
    verified_by: Optional[str] = None

    # Scoring fields - Standardized naming
    total_score: int = Field(default=0)  # Maximum possible score for all tasks in this set
    scored: int = Field(default=0)  # Total score earned across all completed tasks

    # Additional information
    notes: Optional[str] = None
    remark: Optional[str] = None

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )


class TaskSubmission(BaseModel):
    """Model for task submission."""
    task_id: str
    user_id: str
    answers: List[Answer]
    submitted_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    remark: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )


class TaskHistory(BaseModel):
    """Model for task history."""
    id: PyObjectId = object_id_field()
    task_set_id: str
    user_id: str
    input_type: InputType
    tasks: List[TaskItem]
    answers: List[Answer]
    scores: List[int]
    total_score: int  # Maximum possible score for all tasks in this set
    scored: int  # Total score earned across all completed tasks
    created_at: datetime
    submitted_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    remark: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_encoders={ObjectId: str}
    )

    @field_validator("id", mode="before")
    @classmethod
    def validate_object_id(cls, v):
        return validate_object_id(v)
